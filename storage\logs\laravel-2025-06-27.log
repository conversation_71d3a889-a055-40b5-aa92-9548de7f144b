[2025-06-27 14:50:07] production.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at G:\\DL\\app\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:95)
[stacktrace]
#0 {main}
"} 
[2025-06-27 14:56:34] production.ERROR: Maximum execution time of 300 seconds exceeded {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Maximum execution time of 300 seconds exceeded at G:\\DL\\app\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\ServeCommand.php:95)
[stacktrace]
#0 {main}
"} 
[2025-06-27 15:07:17] production.ERROR: Command "run" is not defined.

Did you mean one of these?
    cache:prune-stale-tags
    model:prune
    queue:prune-batches
    queue:prune-failed
    sanctum:prune-expired
    schedule:run {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"run\" is not defined.

Did you mean one of these?
    cache:prune-stale-tags
    model:prune
    queue:prune-batches
    queue:prune-failed
    sanctum:prune-expired
    schedule:run at G:\\DL\\app\\app\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 G:\\DL\\app\\app\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('run')
#1 G:\\DL\\app\\app\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 G:\\DL\\app\\app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 G:\\DL\\app\\app\\artisan(15): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
